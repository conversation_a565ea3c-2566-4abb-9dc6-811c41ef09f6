import { useEffect, useMemo, useState } from 'react';
import { pages } from '../questionnaireConstants';
import { useDispatch, useSelector } from 'react-redux';
import { Box, Button } from '@mui/material';
import {
  GET_ORG_REQUEST_QUERY_KEY,
  IDENTIFIED,
  ORGANIZATION_USER,
  QUESTIONNAIRE_WIDGET,
  actions,
  identifications,
} from '@/containers/commonConstants';
import { Loader, PageNotFound, HeadingAndDescription, Action, LanguageSelector } from '@/components';
import { checkAllRequiredDemographicPresent, getParamFromUrl, scrollToTop } from '@/containers/commonUtility';
import { Questionnaire } from './Questionnaire';
import useNotification from '@/hooks/useNotification';
import { useTranslation } from 'react-i18next';
import { getQuestionnaireWidget } from '@/redux/actions/getQuestionnaireWidget';
import { useParams } from 'next/navigation';

import { Report } from './Report';
import { HeadingAndDescriptionPage } from './HeadingAndDescriptionPage';
import { resetUpdateConnectionAtClientIndex } from '@/redux/actions/updateConnectionAtClientIndex';
import { SignInSignUp } from '@/containers/auth/SignInSignUp';
import { EditDemographic } from '@/containers/Demographics';
import { extractInstrumentScores, getMatchedActionCondition } from '../questionnaireUtility';
import { useQueryClient } from '@tanstack/react-query';
import { validateRequestId } from '@/containers/Demographics/api';
import { isBefore, isEqual } from 'date-fns';
import { useCommonAPIs } from '@/hooks/useCommonAPIs';
import { useSession } from 'next-auth/react';

export const QuestionnaireWidget = (props) => {
  const { questionnaireWidgetSuccessData } = props;
  const dispatch = useDispatch();
  const { t, i18n } = useTranslation();
  const { organizationId, widgetId } = useParams();
  const [, sendNotification] = useNotification();
  const queryClient = useQueryClient();
  const { data: authSession } = useSession();
  const { checkConnectionAtConnectionIndex, checkConnectionAtClientIndex } = useCommonAPIs();

  const { isIndividualUserInfoFetching, individualUserInfoSuccessData } = useSelector(
    (state) => state.individualUserInfoReducer,
  );
  const {
    checkExistingConnectionIndexSuccessData,
    checkExistingConnectionIndexError,
    isCheckExistingConnectionIndexFetching,
  } = useSelector((state) => state.checkExistingConnectionIndexReducer) || {};
  const { checkExistingClientIndexSuccessData, checkExistingClientIndexError, isCheckExistingClientIndexFetching } =
    useSelector((state) => state.checkExistingClientIndexReducer) || {};

  const {
    identification,
    fields,
    flow,
    headingAndDescriptionPage = {
      description: '',
      enabled: false,
      heading: '',
      showDownloadIcon: true,
      showPrintIcon: false,
    },
    isConsentRequired,
    introduction,
    report,
    action,
    showSignIn,
    provinces,
    widgetType,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    selfRegistration,
    otpVerificationEnabled,
    individualNotFoundPage,
    byRequestOnly,
    requestNotFoundPage,
    availableLanguages,
    spinnerText,
  } = questionnaireWidgetSuccessData || {};

  const requestNotFoundPageDetails = {
    heading: requestNotFoundPage?.heading || t('requestNotFound'),
    description: requestNotFoundPage?.description || t('Please get in touch with us'),
  };

  const getInitialPage = () => {
    if (introduction?.enabled) {
      return pages.INTRODUCTION;
    } else if (identification === IDENTIFIED) {
      return pages.DEMOGRAPHIC;
    } else if (identification === identifications.DEIDENTIFIED) {
      return pages.SIGN_IN_FLOW;
    } else {
      return pages.QUESTIONNAIRE;
    }
  };

  const isDemographicFromUrl = getParamFromUrl('demographicPageShow');
  const showDemographicFromUrl = isDemographicFromUrl === 'true';

  const [currentPage, setCurrentPage] = useState(getInitialPage());
  const [demographic, setDemographic] = useState(null);
  const [questionnaireResponse, setQuestionnaireResponse] = useState(null);
  const [isSignInDialogOpen, setIsSignInDialogOpen] = useState(
    identification === identifications.DEIDENTIFIED && !introduction?.enabled,
  );
  const [questionnaireStatus, setQuestionnaireStatus] = useState();
  const [isActionPerformed, setIsActionPerformed] = useState(false);

  useEffect(() => {
    if (questionnaireWidgetSuccessData?.currentLanguage) {
      const languageToUse =
        questionnaireWidgetSuccessData.currentLanguage || questionnaireWidgetSuccessData.defaultLanguage;

      if (languageToUse && i18n.language !== languageToUse) {
        i18n.changeLanguage(languageToUse);
      }
    }
  }, [questionnaireWidgetSuccessData]);

  // use effect to handle requestId validation for de-identified workflow
  useEffect(() => {
    if (checkExistingConnectionIndexSuccessData?.clientId && identification === identifications.DEIDENTIFIED) {
      const requestId = getParamFromUrl('requestId');
      const isRequestIdWorkflow = byRequestOnly || requestId;
      if (isRequestIdWorkflow) {
        handleRequestValidation(requestId, checkExistingConnectionIndexSuccessData?.clientId);
      }
    }
  }, [checkExistingConnectionIndexSuccessData]);

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED && authSession?.user && individualUserInfoSuccessData) {
      if (
        currentPage === pages.SIGN_IN_FLOW &&
        !isCheckExistingConnectionIndexFetching &&
        !checkExistingConnectionIndexSuccessData &&
        !checkExistingConnectionIndexError
      ) {
        checkConnectionAtConnectionIndex();
      }
    }
  }, [authSession, individualUserInfoSuccessData, currentPage, identification]);

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED && checkExistingConnectionIndexSuccessData?.clientId) {
      if (
        !isCheckExistingClientIndexFetching &&
        !checkExistingClientIndexSuccessData &&
        !checkExistingClientIndexError
      ) {
        checkConnectionAtClientIndex();
      }
    }
  }, [checkExistingConnectionIndexSuccessData, identification]);

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED && currentPage === pages.SIGN_IN_FLOW) {
      if (checkExistingConnectionIndexSuccessData || checkExistingConnectionIndexError) {
        if (checkExistingConnectionIndexError) {
          setIsSignInDialogOpen(false);
          setCurrentPage(pages.CLIENT_NOT_FOUND);
        } else if (checkExistingClientIndexSuccessData?.clientId) {
          setIsSignInDialogOpen(false);
          setCurrentPage(pages.QUESTIONNAIRE);
        } else if (checkExistingClientIndexError) {
          setIsSignInDialogOpen(false);
          setCurrentPage(pages.CLIENT_NOT_FOUND);
        }
      }
    }
  }, [
    checkExistingClientIndexSuccessData,
    checkExistingClientIndexError,
    checkExistingConnectionIndexSuccessData,
    checkExistingConnectionIndexError,
    currentPage,
    identification,
  ]);

  useEffect(() => {
    if (identification === identifications.DEIDENTIFIED && authSession?.user && individualUserInfoSuccessData) {
      if (
        currentPage === pages.SIGN_IN_FLOW &&
        !checkExistingConnectionIndexSuccessData &&
        !checkExistingConnectionIndexError &&
        !isCheckExistingConnectionIndexFetching
      ) {
        checkConnectionAtConnectionIndex();
      }
    }
  }, [identification, authSession, individualUserInfoSuccessData]);

  const handleRequestValidation = async (requestId, clientId) => {
    if (!requestId) {
      setCurrentPage(pages.REQUEST_NOT_FOUND);
      return;
    }

    const requestResponse = await queryClient.fetchQuery({
      queryKey: GET_ORG_REQUEST_QUERY_KEY,
      queryFn: () => validateRequestId(requestId, organizationId),
    });

    if (requestResponse?.status === 200) {
      const {
        clientId: apiClientId,
        requestId: apiRequestId,
        organizationId: apiOrganizationId,
        requestedDate,
        expiryDate,
      } = requestResponse?.data || {};
      // Validate API response
      const isClientIdMatch = apiClientId === clientId;
      const isRequestIdMatch = requestId === apiRequestId;
      const isOrganizationIdMatch = organizationId === apiOrganizationId;
      // TODO: Ask Belle if we need to validate date range, if yes, what is the difference between due and expiry date
      // const isDateInRange = isDateWithinRange(requestedDate, dueDate, expiryDate);
      const isBeforeOrEqualExpiry = isBefore(requestedDate, expiryDate) || isEqual(requestedDate, expiryDate);
      if (!isClientIdMatch || !isRequestIdMatch || !isOrganizationIdMatch || !isBeforeOrEqualExpiry) {
        setCurrentPage(pages.REQUEST_NOT_FOUND);
        return;
      }
    } else {
      setCurrentPage(pages.REQUEST_NOT_FOUND);
      return;
    }
  };

  const handleLanguageChange = (lang) => {
    try {
      dispatch(
        getQuestionnaireWidget({
          headers: { 'Content-Type': 'application/json' },
          organizationId,
          widgetId,
          lang,
        }),
      );
      setCurrentPage(getInitialPage());
      setQuestionnaireResponse(null);
      setQuestionnaireStatus(null);
    } catch (error) {
      console.error('Error changing language:', error);
      sendNotification({ msg: t('apiError'), variant: 'error' });
    }
  };

  const matchedAction = useMemo(() => {
    const instrumentScores = extractInstrumentScores(questionnaireResponse);
    const matchedAction = getMatchedActionCondition(action?.metaData?.actionConditions, instrumentScores);

    if (!matchedAction) {
      const defaultAction = action.metaData?.actionConditions?.find((condition) => condition.default);
      return defaultAction;
    }

    return matchedAction;
  }, [questionnaireResponse, action]);

  const handleIntroductionNavigation = (action) => {
    if (action === actions.NEXT) {
      if (flow && flow === ORGANIZATION_USER) {
        // handleSignInAsClinician();
      } else {
        if (identification === identifications.DEIDENTIFIED) {
          if (authSession?.user) {
            if (checkExistingClientIndexSuccessData?.clientId) {
              setCurrentPage(pages.QUESTIONNAIRE);
            } else if (checkExistingClientIndexError) {
              setCurrentPage(pages.CLIENT_NOT_FOUND);
            } else {
              setCurrentPage(pages.SIGN_IN_FLOW);
              if (
                !isCheckExistingConnectionIndexFetching &&
                !checkExistingConnectionIndexSuccessData &&
                !checkExistingConnectionIndexError
              ) {
                checkConnectionAtConnectionIndex();
              }
            }
          } else {
            setCurrentPage(pages.SIGN_IN_FLOW);
            setIsSignInDialogOpen(true);
          }
          return;
        }

        if (isDemographicFromUrl) {
          if (showDemographicFromUrl) {
            setCurrentPage(pages.QUESTIONNAIRE);
            return;
          }
          handleDemographicNavigation(actions.NEXT, demographic);
        } else {
          if (identification === IDENTIFIED) {
            setCurrentPage(pages.DEMOGRAPHIC);
            return;
          }
          setCurrentPage(pages.QUESTIONNAIRE);
        }
      }
    }
  };

  const handleDemographicNavigation = (action, demographic) => {
    setDemographic(demographic);
    if (action === actions.NEXT) {
      dispatch(resetUpdateConnectionAtClientIndex());
      setCurrentPage(pages.QUESTIONNAIRE);
      scrollToTop();
    }
  };

  const handleQuestionnaireNavigation = (action, status, questionnaireResponse) => {
    if (questionnaireResponse) {
      setQuestionnaireResponse(questionnaireResponse);
    }

    const { isSavedForLater = false, isCancelled = false, isQuestionnaireFinished = false } = status || {};
    setQuestionnaireStatus({ isSavedForLater, isCancelled, isQuestionnaireFinished });

    if (action === actions.NEXT) {
      if (isQuestionnaireFinished && report?.enabled) {
        setCurrentPage(pages.RESULT);
        return;
      }
      if (isSavedForLater || isCancelled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
        return;
      }
      if (headingAndDescriptionPage?.enabled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
        return;
      }
      setCurrentPage(pages.QUESTIONNAIRE);
    } else if (action === actions.PREVIOUS) {
      setCurrentPage(pages.DEMOGRAPHIC);
    }
  };

  const handleResultNavigation = () => {
    if (headingAndDescriptionPage?.enabled) {
      setCurrentPage(pages.HEADING_AND_DESCRIPTION);
    } else if (action?.enabled) {
      if (action?.metaData?.actionConditions?.length) {
        setCurrentPage(pages.ACTION_PAGE);
      }
    }
  };

  const handleHeadingAndDescriptionPageNavigation = () => {
    setCurrentPage(pages.ACTION_PAGE);
  };

  const handleActionNavigation = (action) => {
    if (action === actions.NEXT) {
      if (headingAndDescriptionPage?.enabled) {
        setCurrentPage(pages.HEADING_AND_DESCRIPTION);
      } else if (report?.enabled) {
        setCurrentPage(pages.RESULT);
      } else {
        return;
      }
    }
  };

  const handleSignInDialogState = (state) => {
    setIsSignInDialogOpen(state);
  };

  const getCurrentPage = () => {
    let page = <></>;

    switch (currentPage) {
      case pages.INTRODUCTION:
        page = (
          <HeadingAndDescription
            headingAndDescriptionData={introduction}
            handleNavigationCallback={handleIntroductionNavigation}
          />
        );
        break;
      case pages.DEMOGRAPHIC:
        page = (
          <EditDemographic
            title={clientInformationPageTitle}
            subtitle={clientInformationPageSubtitle}
            fields={fields}
            demographic={demographic}
            handleDemographicCreationCallback={handleDemographicNavigation}
            isMultipleIndividualEnabled={false}
            isConsentRequired={isConsentRequired}
            widgetType={widgetType}
            provinces={provinces}
            selfRegistration={selfRegistration}
            clientNotFoundPage={individualNotFoundPage}
            otpVerificationEnabled={otpVerificationEnabled}
            byRequestOnly={byRequestOnly}
            requestNotFoundPage={requestNotFoundPage}
          />
        );
        break;
      case pages.QUESTIONNAIRE:
        page = (
          <Questionnaire
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            handleNavigationCallback={handleQuestionnaireNavigation}
            demographic={demographic}
            setDemographic={setDemographic}
            setIsSignInDialogOpen={handleSignInDialogState}
          />
        );
        break;
      case pages.RESULT:
        page = (
          <Report
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            handleNavigationCallback={handleResultNavigation}
            demographic={demographic}
            isActionPerformed={isActionPerformed}
            setIsActionPerformed={setIsActionPerformed}
            questionnaireResponse={questionnaireResponse}
            signInModalControl={{ isSignInDialogOpen, handleSignInDialogState }}
          />
        );
        break;
      case pages.HEADING_AND_DESCRIPTION:
        page = (
          <HeadingAndDescriptionPage
            questionnaireWidgetSuccessData={questionnaireWidgetSuccessData}
            demographic={demographic}
            questionnaireStatus={questionnaireStatus}
            questionnaireResponse={questionnaireResponse}
            handleNavigationCallback={handleHeadingAndDescriptionPageNavigation}
            isActionPerformed={isActionPerformed}
            setIsActionPerformed={setIsActionPerformed}
          />
        );
        break;
      case pages.ACTION_PAGE:
        page = (
          <Action
            handleNavigationCallback={handleActionNavigation}
            matchedAction={matchedAction}
            demographic={demographic}
            setIsActionPerformed={setIsActionPerformed}
            questionnaireResponse={questionnaireResponse}
          />
        );
        break;
      case pages.REQUEST_NOT_FOUND:
        page = <HeadingAndDescription headingAndDescriptionData={requestNotFoundPageDetails} showNextButton={false} />;
        break;
      case pages.CLIENT_NOT_FOUND:
        page = (
          <HeadingAndDescription
            headingAndDescriptionData={{
              heading: individualNotFoundPage?.heading || t('individualNotFoundHeading'),
              description: individualNotFoundPage?.description || t('individualNotFoundDescription'),
            }}
            showNextButton={false}
          />
        );
        break;
      case pages.SIGN_IN_FLOW:
        if (authSession?.user && (isCheckExistingConnectionIndexFetching || isCheckExistingClientIndexFetching)) {
          page = <Loader active={true} message={t('loading')} />;
        }
        break;
      default:
        page = <PageNotFound />;
        break;
    }

    return page;
  };

  return (
    <>
      <Loader active={isIndividualUserInfoFetching} message={spinnerText || t('loading')} />

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', px: { xs: 1, md: '4.5%' }, mt: 0.5, gap: 0 }}>
        <LanguageSelector availableLanguages={availableLanguages} onLanguageChange={handleLanguageChange} />

        {(showSignIn || identification === identifications.DEIDENTIFIED) && (
          <SignInSignUp
            isSignInDialogOpen={isSignInDialogOpen}
            handleSignInDialogState={handleSignInDialogState}
            widgetType={QUESTIONNAIRE_WIDGET}
            identification={identification}
          />
        )}
      </Box>
      <Box sx={{ mx: 'auto', p: 2 }}>{getCurrentPage()}</Box>
    </>
  );
};
